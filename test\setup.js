// Test setup file
require('dotenv').config();

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ai-chatbot-test';
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-jwt-secret-key';
process.env.OPENAI_API_KEY = process.env.OPENAI_API_KEY || 'test-openai-key';
process.env.GOOGLE_TRANSLATE_API_KEY = process.env.GOOGLE_TRANSLATE_API_KEY || 'test-translate-key';
process.env.PORT = '3001'; // Use different port for tests

// Mock console.log for cleaner test output
const originalLog = console.log;
console.log = (...args) => {
  // Only log errors and important messages during tests
  if (args.some(arg => typeof arg === 'string' && (arg.includes('error') || arg.includes('Error')))) {
    originalLog(...args);
  }
};

// Global test timeout
jest.setTimeout(10000);
