version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: ai-chatbot-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: ai-chatbot
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - chatbot-network

  # AI Chatbot Application
  ai-chatbot:
    build: .
    container_name: ai-chatbot-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      PORT: 3000
      MONGODB_URI: *********************************************************************
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      ADMIN_EMAIL: <EMAIL>
      ADMIN_PASSWORD: admin123
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      GOOGLE_TRANSLATE_API_KEY: ${GOOGLE_TRANSLATE_API_KEY}
      RATE_LIMIT_WINDOW_MS: 900000
      RATE_LIMIT_MAX_REQUESTS: 100
      ALLOWED_ORIGINS: http://localhost:3000,http://localhost:3001
      MAX_CONVERSATION_HISTORY: 20
      AI_RESPONSE_TIMEOUT: 30000
      HUMAN_ESCALATION_KEYWORDS: human,agent,representative,speak to someone
      SUPPORTED_LANGUAGES: en,es,fr,de,it,pt,zh,ja,ko,ar
    depends_on:
      - mongodb
    networks:
      - chatbot-network
    volumes:
      - ./logs:/app/logs

  # Redis for Session Management (Optional)
  redis:
    image: redis:7-alpine
    container_name: ai-chatbot-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - chatbot-network

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: ai-chatbot-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - ai-chatbot
    networks:
      - chatbot-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  chatbot-network:
    driver: bridge
