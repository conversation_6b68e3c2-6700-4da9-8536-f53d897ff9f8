const request = require("supertest");
const express = require("express");

// Create a simple express app for testing
const app = express();
app.use(express.json());

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  });
});

// Root endpoint
app.get("/", (req, res) => {
  res.json({
    message: "AI Customer Support Chatbot API",
    version: "1.0.0",
    endpoints: {
      chat: "/api/chat",
      admin: "/api/admin",
      auth: "/api/auth",
    },
  });
});

// Simple chat session endpoint for testing
app.post("/api/chat/session", (req, res) => {
  res.status(201).json({
    success: true,
    sessionId: "test-session-id",
    conversationId: "test-conversation-id",
    supportedLanguages: [
      { code: "en", name: "English" },
      { code: "es", name: "Spanish" },
    ],
  });
});

// Simple languages endpoint
app.get("/api/chat/languages", (req, res) => {
  res.json({
    success: true,
    languages: [
      { code: "en", name: "English" },
      { code: "es", name: "Spanish" },
      { code: "fr", name: "French" },
    ],
  });
});

// Simple auth endpoint
app.post("/api/auth/login", (req, res) => {
  const { email, password } = req.body;

  if (email === "<EMAIL>" && password === "admin123") {
    res.json({
      success: true,
      token: "test-jwt-token",
      user: { id: "test-user-id", email, role: "admin" },
    });
  } else {
    res.status(401).json({
      success: false,
      error: "Invalid credentials",
    });
  }
});

// Protected route
app.get("/api/admin/dashboard", (req, res) => {
  const authHeader = req.header("Authorization");

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({
      success: false,
      error: "Access denied. No token provided.",
    });
  }

  res.json({
    success: true,
    statistics: {
      conversations: { total: 0, active: 0 },
      messages: { total: 0, today: 0 },
    },
  });
});

describe("Basic API Tests", () => {
  test("Health check endpoint", async () => {
    const response = await request(app).get("/health").expect(200);

    expect(response.body.status).toBe("OK");
    expect(response.body.environment).toBeDefined();
  });

  test("Root endpoint returns API info", async () => {
    const response = await request(app).get("/").expect(200);

    expect(response.body.message).toContain("AI Customer Support Chatbot API");
    expect(response.body.endpoints).toBeDefined();
  });

  test("Get supported languages", async () => {
    const response = await request(app).get("/api/chat/languages").expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.languages).toBeInstanceOf(Array);
    expect(response.body.languages.length).toBeGreaterThan(0);
  });

  test("Create chat session", async () => {
    const customerInfo = {
      name: "Test Customer",
      email: "<EMAIL>",
      platform: "web",
    };

    const response = await request(app)
      .post("/api/chat/session")
      .send({ customerInfo })
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.sessionId).toBeDefined();
    expect(response.body.conversationId).toBeDefined();
  });

  test("Invalid login attempt", async () => {
    const response = await request(app)
      .post("/api/auth/login")
      .send({
        email: "<EMAIL>",
        password: "wrongpassword",
      })
      .expect(401);

    expect(response.body.success).toBe(false);
    expect(response.body.error).toBe("Invalid credentials");
  });

  test("Protected route without token", async () => {
    const response = await request(app).get("/api/admin/dashboard").expect(401);

    expect(response.body.success).toBe(false);
    expect(response.body.error).toContain("No token provided");
  });
});

describe("OpenAI Service Tests", () => {
  const OpenAIService = require("../services/openaiService");

  test("Extract intent from message", () => {
    const openaiService = new OpenAIService();

    expect(openaiService.extractIntent("I need help with billing")).toBe(
      "billing"
    );
    expect(openaiService.extractIntent("The app is not working")).toBe(
      "technical"
    );
    expect(openaiService.extractIntent("How do I reset my password?")).toBe(
      "account"
    );
    expect(openaiService.extractIntent("Hello there")).toBe("general");
  });

  test("Analyze sentiment", () => {
    const openaiService = new OpenAIService();

    expect(openaiService.analyzeSentiment("I love this service!")).toBe(
      "positive"
    );
    expect(openaiService.analyzeSentiment("This is terrible and broken")).toBe(
      "negative"
    );
    expect(openaiService.analyzeSentiment("I need some information")).toBe(
      "neutral"
    );
  });

  test("Extract entities from message", () => {
    const openaiService = new OpenAIService();
    const entities = openaiService.extractEntities(
      "My <NAME_EMAIL> and my order number is ORD123456"
    );

    expect(entities).toEqual(
      expect.arrayContaining([
        expect.objectContaining({ type: "email", value: "<EMAIL>" }),
        expect.objectContaining({ type: "order_number" }),
      ])
    );
  });
});

describe("Translation Service Tests", () => {
  const TranslationService = require("../services/translationService");

  test("Get supported languages", () => {
    const translationService = new TranslationService();
    const languages = translationService.getSupportedLanguages();

    expect(languages).toBeInstanceOf(Array);
    expect(languages.length).toBeGreaterThan(0);
    expect(languages[0]).toHaveProperty("code");
    expect(languages[0]).toHaveProperty("name");
  });

  test("Check language support", () => {
    const translationService = new TranslationService();

    expect(translationService.isLanguageSupported("en")).toBe(true);
    expect(translationService.isLanguageSupported("es")).toBe(true);
    expect(translationService.isLanguageSupported("xyz")).toBe(false);
  });

  test("Get language name", () => {
    const translationService = new TranslationService();

    expect(translationService.getLanguageName("en")).toBe("English");
    expect(translationService.getLanguageName("es")).toBe("Spanish");
    expect(translationService.getLanguageName("xyz")).toBe("XYZ");
  });
});
