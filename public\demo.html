<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Customer Support Chatbot - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #333;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .demo-section {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin-bottom: 50px;
        }

        .demo-section h2 {
            font-size: 2rem;
            margin-bottom: 20px;
            color: #333;
        }

        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .stat-item {
            text-align: center;
            color: white;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .footer {
            text-align: center;
            color: white;
            margin-top: 50px;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }

        /* Chat widget will appear here */
        .chat-trigger {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🤖 AI Customer Support Chatbot</h1>
            <p>Experience the future of customer service with AI-powered conversations</p>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Availability</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">10+</div>
                    <div class="stat-label">Languages</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">&lt;2s</div>
                    <div class="stat-label">Response Time</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">95%</div>
                    <div class="stat-label">Satisfaction</div>
                </div>
            </div>
        </header>

        <section class="features">
            <div class="feature-card">
                <div class="feature-icon">🤖</div>
                <h3>AI-Powered Responses</h3>
                <p>Advanced GPT-4 integration provides intelligent, context-aware responses to customer inquiries with high accuracy and natural conversation flow.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <h3>Live Chat Fallback</h3>
                <p>Seamless escalation to human agents when needed. Smart detection of complex issues that require human intervention.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🌍</div>
                <h3>Multi-Language Support</h3>
                <p>Communicate with customers in their preferred language. Automatic translation and language detection for global reach.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3>Admin Dashboard</h3>
                <p>Comprehensive analytics and conversation management. Monitor performance, assign agents, and track customer satisfaction.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">💬</div>
                <h3>Embeddable Widget</h3>
                <p>Easy integration into any website. Responsive design that works perfectly on desktop and mobile devices.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🔒</div>
                <h3>Secure & Reliable</h3>
                <p>Enterprise-grade security with JWT authentication, rate limiting, and data encryption. Built for scale and reliability.</p>
            </div>
        </section>

        <section class="demo-section">
            <h2>Try It Now</h2>
            <p>Experience the chatbot in action. Click the chat button in the bottom-right corner to start a conversation, or explore the admin dashboard.</p>
            
            <div class="demo-buttons">
                <button class="btn btn-primary" onclick="openChatWidget()">
                    💬 Start Chat Demo
                </button>
                <a href="/admin/dashboard.html" class="btn btn-secondary">
                    📊 View Admin Dashboard
                </a>
                <a href="/widget/chat-widget.html" class="btn btn-secondary" target="_blank">
                    🔗 Open Widget in New Tab
                </a>
            </div>

            <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                <h4>Demo Credentials</h4>
                <p><strong>Admin Login:</strong> <EMAIL> / admin123</p>
                <p><strong>Note:</strong> This is a demo environment. OpenAI API key required for AI responses.</p>
            </div>
        </section>

        <footer class="footer">
            <p>&copy; 2024 AI Customer Support Chatbot. Built with Node.js, Express, Socket.IO, MongoDB, and OpenAI.</p>
            <p>Open source project available on GitHub.</p>
        </footer>
    </div>

    <!-- Chat Widget Integration -->
    <iframe 
        id="chat-widget-frame"
        src="/widget/chat-widget.html" 
        style="position: fixed; bottom: 0; right: 0; width: 400px; height: 600px; border: none; z-index: 9999; display: none;"
        title="Customer Support Chat">
    </iframe>

    <button class="chat-trigger" onclick="toggleChatWidget()" title="Open Chat">
        💬
    </button>

    <script>
        let chatWidgetVisible = false;

        function toggleChatWidget() {
            const frame = document.getElementById('chat-widget-frame');
            chatWidgetVisible = !chatWidgetVisible;
            frame.style.display = chatWidgetVisible ? 'block' : 'none';
        }

        function openChatWidget() {
            const frame = document.getElementById('chat-widget-frame');
            frame.style.display = 'block';
            chatWidgetVisible = true;
        }

        // Auto-show chat widget after 5 seconds for demo
        setTimeout(() => {
            if (!chatWidgetVisible) {
                const trigger = document.querySelector('.chat-trigger');
                trigger.style.animation = 'pulse 1s infinite';
                trigger.style.background = '#ff4757';
            }
        }, 5000);

        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stats on scroll
            const stats = document.querySelectorAll('.stat-number');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'fadeInUp 0.6s ease';
                    }
                });
            });

            stats.forEach(stat => observer.observe(stat));

            // Add hover effects to feature cards
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
