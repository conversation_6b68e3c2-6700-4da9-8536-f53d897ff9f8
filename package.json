{"name": "ai-customer-support-chatbot", "version": "1.0.0", "description": "AI-Powered Customer Support Chatbot with live chat fallback and multi-language support", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "quick-start": "node start.js", "setup": "node start.js", "docker:build": "docker build -t ai-chatbot .", "docker:run": "docker run -p 3000:3000 --env-file .env ai-chatbot", "docker:compose": "docker-compose up -d"}, "keywords": ["chatbot", "ai", "customer-support", "websocket", "openai", "mongodb"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "mongoose": "^7.5.0", "openai": "^4.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "morgan": "^1.10.0", "uuid": "^9.0.0", "@google-cloud/translate": "^8.0.2", "validator": "^13.11.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}