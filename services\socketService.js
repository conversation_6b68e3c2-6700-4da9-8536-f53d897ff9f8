const jwt = require("jsonwebtoken");
const { v4: uuidv4 } = require("uuid");
const Conversation = require("../models/Conversation");
const Message = require("../models/Message");
const User = require("../models/User");
const OpenAIService = require("./openaiService");
const TranslationService = require("./translationService");

class SocketService {
  constructor(io) {
    this.io = io;
    this.openaiService = new OpenAIService();
    this.translationService = new TranslationService();
    this.activeConnections = new Map(); // sessionId -> socket
    this.agentConnections = new Map(); // agentId -> socket
    this.conversationQueues = new Map(); // agentId -> conversation queue
  }

  initialize() {
    this.io.on("connection", (socket) => {
      console.log(`🔌 New connection: ${socket.id}`);

      // Handle customer connections
      socket.on("join_chat", async (data) => {
        await this.handleCustomerJoin(socket, data);
      });

      // Handle agent connections
      socket.on("agent_login", async (data) => {
        await this.handleAgentLogin(socket, data);
      });

      // Handle customer messages
      socket.on("customer_message", async (data) => {
        await this.handleCustomerMessage(socket, data);
      });

      // Handle agent messages
      socket.on("agent_message", async (data) => {
        await this.handleAgentMessage(socket, data);
      });

      // Handle agent taking over conversation
      socket.on("agent_takeover", async (data) => {
        await this.handleAgentTakeover(socket, data);
      });

      // Handle conversation assignment
      socket.on("assign_conversation", async (data) => {
        await this.handleConversationAssignment(socket, data);
      });

      // Handle typing indicators
      socket.on("typing_start", (data) => {
        this.handleTypingStart(socket, data);
      });

      socket.on("typing_stop", (data) => {
        this.handleTypingStop(socket, data);
      });

      // Handle language change
      socket.on("change_language", async (data) => {
        await this.handleLanguageChange(socket, data);
      });

      // Handle conversation rating
      socket.on("rate_conversation", async (data) => {
        await this.handleConversationRating(socket, data);
      });

      // Handle disconnection
      socket.on("disconnect", () => {
        this.handleDisconnection(socket);
      });

      // Handle errors
      socket.on("error", (error) => {
        console.error(`Socket error for ${socket.id}:`, error);
      });
    });
  }

  async handleCustomerJoin(socket, data) {
    try {
      const { sessionId, customerInfo, language = "en" } = data;

      // Generate session ID if not provided
      const finalSessionId = sessionId || uuidv4();

      // Store connection
      this.activeConnections.set(finalSessionId, socket);
      socket.sessionId = finalSessionId;
      socket.userType = "customer";
      socket.language = language;

      // Find or create conversation
      let conversation = await Conversation.findOne({
        sessionId: finalSessionId,
      });

      if (!conversation) {
        conversation = new Conversation({
          sessionId: finalSessionId,
          customerInfo: {
            ...customerInfo,
            ipAddress: socket.handshake.address,
            userAgent: socket.handshake.headers["user-agent"],
          },
          context: {
            language,
            platform: customerInfo?.platform || "web",
          },
          status: "active",
        });
        await conversation.save();
      }

      // Join conversation room
      socket.join(`conversation_${finalSessionId}`);

      // Send welcome message
      const welcomeMessage = await this.createSystemMessage(
        conversation._id,
        finalSessionId,
        "Hello! I'm here to help you. How can I assist you today?",
        language
      );

      socket.emit("conversation_joined", {
        sessionId: finalSessionId,
        conversationId: conversation._id,
        welcomeMessage,
        supportedLanguages: this.translationService.getSupportedLanguages(),
      });

      // Load conversation history
      const messages = await Message.findBySession(finalSessionId, 50);
      const translatedMessages =
        await this.translationService.translateConversation(messages, language);

      socket.emit("conversation_history", {
        messages: translatedMessages,
      });

      console.log(`👤 Customer joined: ${finalSessionId}`);
    } catch (error) {
      console.error("Customer join error:", error);
      socket.emit("error", { message: "Failed to join chat" });
    }
  }

  async handleAgentLogin(socket, data) {
    try {
      const { token } = data;

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const agent = await User.findById(decoded.userId);

      if (!agent || !["admin", "agent"].includes(agent.role)) {
        socket.emit("auth_error", { message: "Unauthorized" });
        return;
      }

      // Store agent connection
      this.agentConnections.set(agent._id.toString(), socket);
      socket.agentId = agent._id.toString();
      socket.userType = "agent";
      socket.agentInfo = agent;

      // Join agent room
      socket.join("agents");
      socket.join(`agent_${agent._id}`);

      // Initialize conversation queue for agent
      if (!this.conversationQueues.has(agent._id.toString())) {
        this.conversationQueues.set(agent._id.toString(), []);
      }

      // Get assigned conversations
      const assignedConversations = await Conversation.findByAgent(agent._id)
        .populate("customerInfo")
        .sort({ lastActivity: -1 });

      // Get waiting conversations
      const waitingConversations = await Conversation.findWaiting()
        .populate("customerInfo")
        .limit(10);

      socket.emit("agent_authenticated", {
        agent: agent.toJSON(),
        assignedConversations,
        waitingConversations,
      });

      // Notify other agents
      socket.to("agents").emit("agent_online", {
        agentId: agent._id,
        name: agent.name,
      });

      console.log(`👨‍💼 Agent logged in: ${agent.name} (${agent._id})`);
    } catch (error) {
      console.error("Agent login error:", error);
      socket.emit("auth_error", { message: "Authentication failed" });
    }
  }

  async handleCustomerMessage(socket, data) {
    try {
      const { text, language } = data;
      const sessionId = socket.sessionId;

      if (!sessionId || !text?.trim()) {
        socket.emit("error", { message: "Invalid message data" });
        return;
      }

      // Find conversation
      const conversation = await Conversation.findOne({ sessionId });
      if (!conversation) {
        socket.emit("error", { message: "Conversation not found" });
        return;
      }

      // Create user message
      const userMessage = new Message({
        conversationId: conversation._id,
        sessionId,
        sender: {
          type: "user",
          name: conversation.customerInfo.name || "Customer",
        },
        content: {
          text: text.trim(),
          language: language || socket.language || "en",
        },
        metadata: {
          messageType: "text",
        },
      });

      await userMessage.save();

      // Update conversation analytics
      conversation.analytics.totalMessages += 1;
      conversation.lastActivity = new Date();
      await conversation.save();

      // Broadcast to conversation room
      this.io.to(`conversation_${sessionId}`).emit("new_message", userMessage);

      // If conversation is assigned to an agent, send to agent
      if (conversation.assignedAgent) {
        const agentSocket = this.agentConnections.get(
          conversation.assignedAgent.toString()
        );
        if (agentSocket) {
          agentSocket.emit("customer_message_received", {
            conversationId: conversation._id,
            message: userMessage,
          });
        }
      } else {
        // Generate AI response
        await this.generateAIResponse(conversation, userMessage);
      }
    } catch (error) {
      console.error("Customer message error:", error);
      socket.emit("error", { message: "Failed to send message" });
    }
  }

  async generateAIResponse(conversation, userMessage) {
    try {
      // Get conversation history
      const history = await Message.findByConversation(conversation._id, 10);

      // Generate AI response
      const aiResponse = await this.openaiService.generateResponse(
        userMessage.content.text,
        history,
        conversation.customerInfo
      );

      // Check if escalation is needed
      if (aiResponse.shouldEscalate) {
        await this.escalateConversation(
          conversation,
          aiResponse.escalationReason
        );
        return;
      }

      // Create AI message
      const aiMessage = new Message({
        conversationId: conversation._id,
        sessionId: conversation.sessionId,
        sender: {
          type: "ai",
          name: "AI Assistant",
        },
        content: {
          text: aiResponse.text,
          language: userMessage.content.language,
        },
        metadata: {
          messageType: "text",
          intent: aiResponse.intent,
          confidence: aiResponse.confidence,
          entities: aiResponse.entities,
          sentiment: aiResponse.sentiment,
          aiModel: aiResponse.model,
          processingTime: aiResponse.processingTime,
          tokens: aiResponse.tokens,
        },
      });

      await aiMessage.save();

      // Update conversation analytics
      conversation.analytics.totalMessages += 1;
      conversation.analytics.aiMessages += 1;

      if (!conversation.analytics.firstResponseTime) {
        conversation.analytics.firstResponseTime = new Date();
      }

      await conversation.save();

      // Send AI response to customer
      this.io
        .to(`conversation_${conversation.sessionId}`)
        .emit("new_message", aiMessage);
    } catch (error) {
      console.error("AI response generation error:", error);

      // Send fallback message and escalate
      const fallbackMessage = new Message({
        conversationId: conversation._id,
        sessionId: conversation.sessionId,
        sender: {
          type: "ai",
          name: "AI Assistant",
        },
        content: {
          text: "I apologize, but I'm experiencing technical difficulties. Let me connect you with a human agent who can assist you.",
          language: userMessage.content.language,
        },
        metadata: {
          messageType: "text",
        },
      });

      await fallbackMessage.save();
      this.io
        .to(`conversation_${conversation.sessionId}`)
        .emit("new_message", fallbackMessage);

      await this.escalateConversation(conversation, "AI service error");
    }
  }

  async escalateConversation(conversation, reason) {
    try {
      // Update conversation status
      await conversation.escalate(reason);

      // Notify available agents
      this.io.to("agents").emit("conversation_escalated", {
        conversationId: conversation._id,
        sessionId: conversation.sessionId,
        customerInfo: conversation.customerInfo,
        reason,
        priority: conversation.priority,
      });

      // Notify customer
      const customerSocket = this.activeConnections.get(conversation.sessionId);
      if (customerSocket) {
        customerSocket.emit("conversation_escalated", {
          message:
            "You're being connected to a human agent. Please wait a moment.",
          estimatedWaitTime: "2-3 minutes",
        });
      }

      console.log(
        `🚨 Conversation escalated: ${conversation.sessionId} - ${reason}`
      );
    } catch (error) {
      console.error("Escalation error:", error);
    }
  }

  async handleAgentMessage(socket, data) {
    try {
      const { conversationId, text, language } = data;
      const agentId = socket.agentId;

      if (!agentId || !conversationId || !text?.trim()) {
        socket.emit("error", { message: "Invalid message data" });
        return;
      }

      // Find conversation
      const conversation = await Conversation.findById(conversationId);
      if (!conversation) {
        socket.emit("error", { message: "Conversation not found" });
        return;
      }

      // Create agent message
      const agentMessage = new Message({
        conversationId: conversation._id,
        sessionId: conversation.sessionId,
        sender: {
          type: "agent",
          id: agentId,
          name: socket.agentInfo.name,
          avatar: socket.agentInfo.profile?.avatar,
        },
        content: {
          text: text.trim(),
          language: language || "en",
        },
        metadata: {
          messageType: "text",
        },
      });

      await agentMessage.save();

      // Update conversation
      conversation.analytics.totalMessages += 1;
      conversation.analytics.humanMessages += 1;
      conversation.lastActivity = new Date();

      if (conversation.type === "ai") {
        conversation.type = "hybrid";
      }

      await conversation.save();

      // Send to customer
      this.io
        .to(`conversation_${conversation.sessionId}`)
        .emit("new_message", agentMessage);

      // Send to other agents monitoring this conversation
      socket.to("agents").emit("agent_message_sent", {
        conversationId: conversation._id,
        message: agentMessage,
      });
    } catch (error) {
      console.error("Agent message error:", error);
      socket.emit("error", { message: "Failed to send message" });
    }
  }

  async createSystemMessage(conversationId, sessionId, text, language = "en") {
    const systemMessage = new Message({
      conversationId,
      sessionId,
      sender: {
        type: "ai",
        name: "System",
      },
      content: {
        text,
        language,
      },
      metadata: {
        messageType: "system",
      },
    });

    await systemMessage.save();
    return systemMessage;
  }

  handleTypingStart(socket, data) {
    const { conversationId } = data;

    if (socket.userType === "customer") {
      // Notify assigned agent
      socket.to("agents").emit("customer_typing", {
        conversationId,
        sessionId: socket.sessionId,
      });
    } else if (socket.userType === "agent") {
      // Notify customer
      socket.to(`conversation_${data.sessionId}`).emit("agent_typing", {
        agentName: socket.agentInfo.name,
      });
    }
  }

  handleTypingStop(socket, data) {
    const { conversationId } = data;

    if (socket.userType === "customer") {
      socket.to("agents").emit("customer_typing_stop", {
        conversationId,
        sessionId: socket.sessionId,
      });
    } else if (socket.userType === "agent") {
      socket.to(`conversation_${data.sessionId}`).emit("agent_typing_stop");
    }
  }

  async handleAgentTakeover(socket, data) {
    try {
      const { conversationId } = data;
      const agentId = socket.agentId;

      if (!agentId || !conversationId) {
        socket.emit("error", { message: "Invalid takeover data" });
        return;
      }

      // Find conversation
      const conversation = await Conversation.findById(conversationId);
      if (!conversation) {
        socket.emit("error", { message: "Conversation not found" });
        return;
      }

      // Update conversation
      conversation.assignedAgent = agentId;
      conversation.status = "active";
      conversation.type = "human";
      conversation.analytics.handoffTime = new Date();

      await conversation.save();

      // Notify customer
      const customerSocket = this.activeConnections.get(conversation.sessionId);
      if (customerSocket) {
        customerSocket.emit("agent_takeover", {
          agentName: socket.agentInfo.name,
          message: `${socket.agentInfo.name} has joined the conversation.`,
        });
      }

      // Notify other agents
      socket.to("agents").emit("conversation_assigned", {
        conversationId: conversation._id,
        agentId,
        agentName: socket.agentInfo.name,
      });

      socket.emit("takeover_success", {
        conversationId: conversation._id,
        message: "Successfully took over conversation",
      });
    } catch (error) {
      console.error("Agent takeover error:", error);
      socket.emit("error", { message: "Failed to take over conversation" });
    }
  }

  async handleConversationAssignment(socket, data) {
    try {
      const { conversationId, agentId } = data;

      // Only admins can assign conversations
      if (socket.agentInfo.role !== "admin") {
        socket.emit("error", {
          message: "Only admins can assign conversations",
        });
        return;
      }

      // Find conversation and agent
      const [conversation, agent] = await Promise.all([
        Conversation.findById(conversationId),
        User.findById(agentId),
      ]);

      if (!conversation) {
        socket.emit("error", { message: "Conversation not found" });
        return;
      }

      if (!agent || !["admin", "agent"].includes(agent.role)) {
        socket.emit("error", { message: "Invalid agent" });
        return;
      }

      // Update conversation
      conversation.assignedAgent = agentId;
      conversation.status = "active";
      conversation.type = "human";
      conversation.analytics.handoffTime = new Date();

      await conversation.save();

      // Notify assigned agent
      const agentSocket = this.agentConnections.get(agentId);
      if (agentSocket) {
        agentSocket.emit("conversation_assigned", {
          conversationId: conversation._id,
          conversation,
        });
      }

      // Notify customer
      const customerSocket = this.activeConnections.get(conversation.sessionId);
      if (customerSocket) {
        customerSocket.emit("agent_assigned", {
          agentName: agent.name,
          message: `${agent.name} has been assigned to help you.`,
        });
      }

      // Notify all agents
      this.io.to("agents").emit("conversation_update", {
        conversationId: conversation._id,
        assignedAgent: agentId,
        status: "active",
      });

      socket.emit("assignment_success", {
        conversationId: conversation._id,
        agentId,
        message: "Conversation assigned successfully",
      });
    } catch (error) {
      console.error("Conversation assignment error:", error);
      socket.emit("error", { message: "Failed to assign conversation" });
    }
  }

  async handleLanguageChange(socket, data) {
    try {
      const { language, sessionId } = data;

      if (!sessionId || !language) {
        socket.emit("error", { message: "Invalid language change data" });
        return;
      }

      // Update socket language
      socket.language = language;

      // Update conversation context
      const conversation = await Conversation.findOne({ sessionId });
      if (conversation) {
        conversation.context.language = language;
        await conversation.save();
      }

      // Get translated conversation history
      const messages = await Message.findRecentBySession(sessionId, 20);
      const translatedMessages =
        await this.translationService.translateConversation(messages, language);

      socket.emit("language_changed", {
        language,
        messages: translatedMessages,
      });
    } catch (error) {
      console.error("Language change error:", error);
      socket.emit("error", { message: "Failed to change language" });
    }
  }

  async handleConversationRating(socket, data) {
    try {
      const { rating, feedback, sessionId } = data;

      if (!sessionId || !rating || rating < 1 || rating > 5) {
        socket.emit("error", { message: "Invalid rating data" });
        return;
      }

      // Find conversation
      const conversation = await Conversation.findOne({ sessionId });
      if (!conversation) {
        socket.emit("error", { message: "Conversation not found" });
        return;
      }

      // Update conversation rating
      conversation.metadata.satisfaction = {
        rating,
        feedback: feedback || "",
        timestamp: new Date(),
      };

      await conversation.save();

      socket.emit("rating_submitted", {
        message: "Thank you for your feedback!",
      });

      // Notify agents about the rating
      this.io.to("agents").emit("conversation_rated", {
        conversationId: conversation._id,
        rating,
        feedback,
      });
    } catch (error) {
      console.error("Conversation rating error:", error);
      socket.emit("error", { message: "Failed to submit rating" });
    }
  }

  handleDisconnection(socket) {
    console.log(`🔌 Disconnection: ${socket.id}`);

    if (socket.userType === "customer" && socket.sessionId) {
      this.activeConnections.delete(socket.sessionId);
    } else if (socket.userType === "agent" && socket.agentId) {
      this.agentConnections.delete(socket.agentId);

      // Notify other agents
      socket.to("agents").emit("agent_offline", {
        agentId: socket.agentId,
      });
    }
  }
}

module.exports = SocketService;
