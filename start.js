#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🚀 AI Customer Support Chatbot - Quick Start');
console.log('============================================\n');

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.log('📝 Creating .env file from template...');
  
  const envExample = fs.readFileSync(path.join(__dirname, '.env.example'), 'utf8');
  fs.writeFileSync(envPath, envExample);
  
  console.log('✅ .env file created successfully!');
  console.log('\n⚠️  IMPORTANT: Please edit the .env file with your configuration:');
  console.log('   - Add your OpenAI API key');
  console.log('   - Add your MongoDB connection string');
  console.log('   - Add your Google Translate API key (optional)');
  console.log('   - Update JWT secret and admin credentials\n');
  
  console.log('📖 For detailed setup instructions, see README.md\n');
  
  // Ask user if they want to continue
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('Do you want to continue with the default configuration? (y/N): ', (answer) => {
    rl.close();
    
    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.log('\n📝 Please edit the .env file and run this script again.');
      process.exit(0);
    }
    
    startServer();
  });
} else {
  console.log('✅ .env file found');
  startServer();
}

function startServer() {
  console.log('\n🔧 Starting AI Customer Support Chatbot...\n');
  
  // Check if MongoDB is required
  const env = require('dotenv').config();
  const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/ai-chatbot';
  
  if (mongoUri.includes('localhost')) {
    console.log('📦 Make sure MongoDB is running locally on port 27017');
    console.log('   Or update MONGODB_URI in .env to use a cloud database\n');
  }
  
  // Start the server
  const serverProcess = spawn('node', ['server.js'], {
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'development' }
  });
  
  serverProcess.on('error', (error) => {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  });
  
  serverProcess.on('exit', (code) => {
    if (code !== 0) {
      console.error(`❌ Server exited with code ${code}`);
      process.exit(code);
    }
  });
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    serverProcess.kill('SIGINT');
  });
  
  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    serverProcess.kill('SIGTERM');
  });
  
  // Show helpful information
  setTimeout(() => {
    console.log('\n🎉 Server should be starting up!');
    console.log('\n📱 Access Points:');
    console.log('   • Demo Page: http://localhost:3000/demo.html');
    console.log('   • Chat Widget: http://localhost:3000/widget/chat-widget.html');
    console.log('   • Admin Dashboard: http://localhost:3000/admin/dashboard.html');
    console.log('   • API Health: http://localhost:3000/health');
    console.log('\n🔑 Default Admin Credentials:');
    console.log('   • Email: <EMAIL>');
    console.log('   • Password: admin123');
    console.log('\n💡 Tips:');
    console.log('   • Initialize admin user: curl -X POST http://localhost:3000/api/auth/init-admin');
    console.log('   • View logs in the terminal');
    console.log('   • Press Ctrl+C to stop the server');
    console.log('\n📚 For more information, check the README.md file');
  }, 2000);
}
